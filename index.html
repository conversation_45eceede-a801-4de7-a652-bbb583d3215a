<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Kanban Board</title>
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <header>
    <h1>Kanban Board</h1>
  </header>

  <section class="task-creator">
    <form id="task-form">
      <input type="text" id="task-title" placeholder="Task title" required />
      <input type="text" id="task-desc" placeholder="Task description (optional)" />
      <button type="submit">Add Task</button>
    </form>
  </section>

  <main class="board">
    <section class="column" data-status="todo">
      <h2>Todo</h2>
      <div class="task-list" id="todo-tasks" ondragover="event.preventDefault()" ondrop="handleDrop(event, 'todo')"></div>
    </section>
    <section class="column" data-status="in-progress">
      <h2>In Progress</h2>
      <div class="task-list" id="in-progress-tasks" ondragover="event.preventDefault()" ondrop="handleDrop(event, 'in-progress')"></div>
    </section>
    <section class="column" data-status="done">
      <h2>Done</h2>
      <div class="task-list" id="done-tasks" ondragover="event.preventDefault()" ondrop="handleDrop(event, 'done')"></div>
    </section>
  </main>

  <script type="module" src="main.js"></script>
</body>
</html>
