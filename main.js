import confetti from 'canvas-confetti';

let tasks = [];

// Load tasks from localStorage
function loadTasks() {
  const stored = localStorage.getItem('kanban-tasks');
  if (stored) {
    tasks = JSON.parse(stored);
  } else {
    tasks = [];
  }
}

// Save tasks to localStorage
function saveTasks() {
  localStorage.setItem('kanban-tasks', JSON.stringify(tasks));
}

// Render tasks to the board
function renderTasks() {
  ['todo', 'in-progress', 'done'].forEach(status => {
    const container = document.getElementById(`${status}-tasks`);
    container.innerHTML = '';
    tasks.filter(t => t.status === status).forEach(task => {
      const div = document.createElement('div');
      div.className = 'task';
      div.draggable = true;
      div.dataset.id = task.id;

      div.addEventListener('dragstart', handleDragStart);

      div.innerHTML = `
        <div class="title">${task.title}</div>
        ${task.description ? `<div class="description">${task.description}</div>` : ''}
        <button class="delete-btn" aria-label="Delete task">&times;</button>
      `;

      // Delete button
      div.querySelector('.delete-btn').addEventListener('click', (e) => {
        e.stopPropagation();
        deleteTask(task.id);
      });

      container.appendChild(div);
    });
  });
}

// Add new task
function addTask(title, description) {
  const newTask = {
    id: Date.now().toString(),
    title,
    description,
    status: 'todo'
  };
  tasks.push(newTask);
  saveTasks();
  renderTasks();
}

// Delete task
function deleteTask(id) {
  tasks = tasks.filter(t => t.id !== id);
  saveTasks();
  renderTasks();
}

// Drag and Drop Handlers
let dragSrcId = null;

function handleDragStart(e) {
  dragSrcId = e.target.dataset.id;
  e.dataTransfer.effectAllowed = 'move';
  e.dataTransfer.setData('text/plain', dragSrcId);
}

// Drop handler
window.handleDrop = function (e, newStatus) {
  e.preventDefault();
  const id = e.dataTransfer.getData('text/plain');

  const task = tasks.find(t => t.id === id);
  if (task && task.status !== newStatus) {
    task.status = newStatus;
    saveTasks();
    renderTasks();

    // Trigger confetti on move to done
    if (newStatus === 'done') {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
    }
  }
};

// Form submit handler
const form = document.getElementById('task-form');
form.addEventListener('submit', e => {
  e.preventDefault();
  const title = document.getElementById('task-title').value.trim();
  const desc = document.getElementById('task-desc').value.trim();
  if (title) {
    addTask(title, desc);
    form.reset();
  }
});

// Initial setup
loadTasks();
renderTasks();
