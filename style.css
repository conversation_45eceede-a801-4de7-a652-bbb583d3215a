* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

body {
  background-color: #f7f8fa;
  color: #333;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
}

header {
  margin-bottom: 1.5rem;
}

h1 {
  font-weight: 700;
  font-size: 2rem;
  color: #1e293b;
}

.task-creator {
  margin-bottom: 1.5rem;
  width: 100%;
  max-width: 600px;
}

#task-form {
  display: flex;
  gap: 0.5rem;
}

#task-title,
#task-desc {
  flex: 1;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  outline-offset: 2px;
}

button {
  padding: 0 1.25rem;
  border: none;
  background-color: #3b82f6;
  color: white;
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

button:hover {
  background-color: #2563eb;
}

.board {
  display: flex;
  gap: 1rem;
  width: 100%;
  max-width: 900px;
  justify-content: center;
  padding-bottom: 2rem;
}

.column {
  background: white;
  border-radius: 10px;
  flex: 1;
  min-width: 280px;
  display: flex;
  flex-direction: column;
  max-height: 75vh;
  overflow: auto;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 4px rgb(0 0 0 / 0.05);
  padding: 1rem;
}

.column h2 {
  font-weight: 700;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: #1e293b;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  flex-grow: 1;
}

.task {
  padding: 0.75rem 1rem;
  background-color: #e0e7ff;
  border-radius: 8px;
  cursor: grab;
  box-shadow: 0 1px 3px rgb(0 0 0 / 0.1);
  user-select: none;
  transition: background-color 0.2s ease;
}

.task:active {
  cursor: grabbing;
  background-color: #c7d2fe;
}

.task .title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
  color: #1e293b;
}

.task .description {
  font-size: 0.875rem;
  color: #4b5563;
  white-space: pre-wrap;
}

.task .delete-btn {
  margin-top: 0.5rem;
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-weight: 700;
  font-size: 0.9rem;
  padding: 0;
  user-select: none;
  transition: color 0.2s ease;
}

.task .delete-btn:hover {
  color: #b91c1c;
}

@media (max-width: 768px) {
  .board {
    flex-direction: column;
    max-width: 100%;
  }

  .column {
    min-width: 100%;
    max-height: unset;
  }
}
